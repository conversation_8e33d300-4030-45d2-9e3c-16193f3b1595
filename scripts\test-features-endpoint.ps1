# Simple test script to verify the features endpoint works

$ApiUrl = "http://localhost:1337/api"

Write-Host "🧪 Testing Features Endpoint" -ForegroundColor Green

try {
    Write-Host "📡 Fetching features options..." -ForegroundColor Yellow
    $response = Invoke-RestMethod -Uri "$ApiUrl/properties/features-options"
    
    Write-Host "✅ Features endpoint working!" -ForegroundColor Green
    Write-Host "📊 Found $($response.data.Count) features:" -ForegroundColor Gray
    
    foreach ($feature in $response.data) {
        Write-Host "   • $($feature.value) → $($feature.label)" -ForegroundColor Gray
    }
    
} catch {
    Write-Error "❌ Failed to fetch features: $($_.Exception.Message)"
    exit 1
}

Write-Host ""
Write-Host "🎉 Test completed successfully!" -ForegroundColor Green
