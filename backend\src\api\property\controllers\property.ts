/**
 * property controller
 */

import { factories } from '@strapi/strapi'
import { fetchProperties, fetchPropertyById, fetchPropertyBySlug, POPULATE_CONFIGS } from '../helpers/propertyFetcher';
import viewTracker from '../services/viewTracker';

// Helper function to format enum values to display labels
const formatEnumLabel = (value: string): string => {
  return value
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};



export default factories.createCoreController('api::property.property', ({ strapi }) => ({
  // Custom find method using reusable helper for better control and consistency
  async find(ctx) {
    try {
      // Use the reusable fetchProperties helper with list population
      const result = await fetchProperties({
        query: ctx.query,
        populationType: 'list',
        pagination: { page: 1, pageSize: 20 },
        defaultSort: { createdAt: 'desc' }
      });

      // Sanitize output and transform response
      const sanitized = await this.sanitizeOutput(result.data, ctx);

      return this.transformResponse(sanitized, result.meta);
    } catch (error) {
      console.error('Error in property find:', error);
      return ctx.internalServerError('Failed to fetch properties');
    }
  },

  // Custom findOne method
  async findOne(ctx) {
    const { id } = ctx.params;

    console.log('findOne called with ID:', id, 'Type:', typeof id);

    try {
      // Try to find by documentId first (Strapi v5), then by numeric ID
      let property;
      let actualId = id; // The ID we'll use for the update

      // Check if ID looks like a documentId (long string), numeric ID, or slug
      const isNumericId = /^\d+$/.test(id);
      const isDocumentId = /^[a-z0-9]{25,}$/.test(id); // Strapi v5 documentId pattern
      const isSlug = !isNumericId && !isDocumentId; // Assume it's a slug if not numeric or documentId

      console.log('ID type detection:', { isNumericId, isDocumentId, isSlug });

      // Try slug lookup first if it looks like a slug
      if (isSlug) {
        try {
          console.log('Attempting slug lookup...');
          const properties = await strapi.entityService.findMany(
            'api::property.property',
            {
              filters: { slug: { $eq: id } },
              populate: ['owner', 'agent', 'images', 'floorPlan', 'project']
            }
          ) as any[];
          property = properties.length > 0 ? properties[0] : null;
          console.log('Slug lookup result:', property ? `Found property ${property.id} (${property.documentId})` : 'Not found');
          if (property) {
            actualId = property.documentId; // Use documentId for update
          }
        } catch (slugError) {
          console.log('Slug lookup failed:', slugError.message);
          property = null;
        }
      }

      if (!property && !isNumericId) {
        // Try with documentId using findOne directly
        try {
          console.log('Attempting documentId lookup with findOne...');
          property = await strapi.entityService.findOne(
            'api::property.property',
            id,
            {
              populate: ['owner', 'agent', 'images', 'floorPlan', 'project']
            }
          ) as any;
          console.log('Found property with documentId:', property ? `${property.id} (${property.documentId})` : 'Not found');
          if (property) {
            actualId = property.documentId; // Use documentId for update
          }
        } catch (error) {
          console.log('DocumentId search failed:', error.message);
          property = null;
        }

        // If documentId lookup didn't work, try manual matching
        if (!property) {
          try {
            console.log('Trying manual documentId matching...');
            const allProperties = await strapi.entityService.findMany(
              'api::property.property',
              {
                populate: ['owner', 'agent', 'images', 'floorPlan', 'project']
              }
            ) as any[];

            console.log(`Found ${allProperties.length} properties to search through`);
            console.log('Looking for documentId:', id);

            property = allProperties.find(p => {
              console.log(`Checking property ${p.id} with documentId: ${p.documentId}`);
              return p.documentId === id;
            }) || null;

            console.log('Manual documentId match result:', property ? `${property.id} (${property.documentId})` : 'Not found');
            if (property) {
              actualId = property.documentId; // Use documentId for update
            }
          } catch (manualError) {
            console.log('Manual documentId matching failed:', manualError.message);
            property = null;
          }
        }
      } else {
        // Try with numeric ID
        try {
          console.log('Attempting numeric ID lookup...');
          property = await strapi.entityService.findOne(
            'api::property.property',
            parseInt(id),
            {
              populate: ['owner', 'agent', 'images', 'floorPlan', 'project']
            }
          ) as any;
          console.log('Found property with numeric ID:', property ? `${property.id} (${property.documentId})` : 'Not found');
          if (property) {
            actualId = property.documentId; // Use documentId for update
          }
        } catch (numericError) {
          console.log('Numeric ID search failed:', numericError.message);
          property = null;
        }
      }

      if (!property) {
        console.log('Property not found for ID:', id);
        return ctx.notFound('Property not found');
      }

      console.log('Tracking view for property:', property.id, 'actualId:', actualId);

      try {
        // Extract user information for session tracking
        const userId = ctx.state.user?.id?.toString();

        // Track view asynchronously (completely non-blocking)
        const wasTracked = await viewTracker.trackView(actualId, ctx.request.headers['user-agent'], ctx.request.ip, userId);

        // Always get and display current view count (regardless of whether this specific view was tracked)
        const displayViews = await viewTracker.getDisplayViewCount(actualId);
        property.views = displayViews;

        console.log(`Property views (display): ${displayViews} ${wasTracked ? '(view tracked)' : '(view throttled/filtered)'}`);
      } catch (viewError) {
        console.error('Error tracking view, using existing count:', viewError);
        // Don't fail the request if view tracking fails
        property.views = property.views || 0;
      }

      console.log('Returning property data with view count:', property.views);
      return { data: property };
    } catch (error) {
      console.error('Error in findOne:', error);
      return ctx.internalServerError('Failed to fetch property');
    }
  },

  // Find property by slug using reusable helper
  async findBySlug(ctx) {
    const { slug } = ctx.params;

    console.log('findBySlug called with slug:', slug);

    try {
      // Use the reusable fetchPropertyBySlug helper
      const property = await fetchPropertyBySlug(slug, 'detailed');

      if (!property) {
        return ctx.notFound('Property not found');
      }

      try {
        console.log('Tracking view for property by slug:', property.documentId);

        // Extract user information for session tracking
        const userId = ctx.state.user?.id?.toString();

        // Track view asynchronously (completely non-blocking)
        const wasTracked = await viewTracker.trackView(property.documentId, ctx.request.headers['user-agent'], ctx.request.ip, userId);

        // Always get and display current view count (regardless of whether this specific view was tracked)
        const displayViews = await viewTracker.getDisplayViewCount(property.documentId);
        property.views = displayViews;

        console.log(`Property views (display): ${displayViews} ${wasTracked ? '(view tracked)' : '(view throttled/filtered)'}`);
      } catch (viewError) {
        console.error('Error tracking view, using existing count:', viewError);
        // Don't fail the request if view tracking fails
        property.views = property.views || 0;
      }

      return { data: property };
    } catch (error) {
      console.error('Error in findBySlug:', error);
      return ctx.internalServerError('An error occurred while fetching the property');
    }
  },

  // Get property for editing (without incrementing view count)
  async getForEdit(ctx) {
    const { id } = ctx.params;
    const user = ctx.state.user;

    console.log('getForEdit called with ID:', id);
    console.log('User:', user ? user.id : 'No user');

    if (!user) {
      return ctx.unauthorized('You must be logged in');
    }

    try {
      // Try to find by documentId first (Strapi v5), then by numeric ID
      let property;

      console.log('Trying to find property with ID:', id, 'Type:', typeof id);

      // Check if ID looks like a documentId (long string) or numeric ID
      const isNumericId = /^\d+$/.test(id);
      console.log('Is numeric ID:', isNumericId);

      if (!isNumericId) {
        // Try with documentId using findOne directly (this should work in Strapi v5)
        try {
          console.log('Attempting documentId lookup with findOne...');
          property = await strapi.entityService.findOne(
            'api::property.property',
            id,
            {
              populate: ['owner', 'agent', 'images', 'floorPlan', 'project']
            }
          ) as any;
          console.log('Found property with documentId:', property ? `${property.id} (${property.documentId})` : 'Not found');
        } catch (error) {
          console.log('DocumentId search failed:', error.message);
          property = null;
        }

        // If documentId lookup didn't work, try manual matching
        if (!property) {
          try {
            console.log('Trying manual documentId matching...');
            const allProperties = await strapi.entityService.findMany(
              'api::property.property',
              {
                populate: ['owner', 'agent', 'images', 'floorPlan', 'project']
              }
            ) as any[];

            console.log(`Found ${allProperties.length} properties to search through`);
            console.log('Looking for documentId:', id);

            property = allProperties.find(p => {
              console.log(`Checking property ${p.id} with documentId: ${p.documentId}`);
              return p.documentId === id;
            }) || null;

            console.log('Manual documentId match result:', property ? `${property.id} (${property.documentId})` : 'Not found');
          } catch (manualError) {
            console.log('Manual documentId matching failed:', manualError.message);
            property = null;
          }
        }
      } else {
        // Try with numeric ID
        try {
          console.log('Attempting numeric ID lookup...');
          const properties = await strapi.entityService.findMany(
            'api::property.property',
            {
              filters: { id: parseInt(id) },
              populate: ['owner', 'agent', 'images', 'floorPlan', 'project']
            }
          ) as any[];

          property = properties.length > 0 ? properties[0] : null;
          console.log('Found property with numeric ID:', property ? `${property.id} (${property.documentId})` : 'Not found');
        } catch (numericError) {
          console.log('Numeric ID search failed:', numericError.message);
          property = null;
        }
      }

      if (!property) {
        console.log('Property not found for ID:', id);
        return ctx.notFound('Property not found');
      }

      // Check if user is owner or admin
      const ownerId = property.owner?.id || property.owner;
      console.log('Property owner ID:', ownerId, 'User ID:', user.id);

      if (ownerId !== user.id && user.role?.name !== 'Admin') {
        console.log('Access denied - user is not owner or admin');
        return ctx.forbidden('You can only edit your own properties');
      }

      console.log('Returning property data for editing');
      // Return property without incrementing view count
      return { data: property };
    } catch (error) {
      console.error('Error in getForEdit:', error);
      return ctx.internalServerError('Failed to fetch property for editing');
    }
  },

  // Custom create method to set owner
  async create(ctx) {
    const user = ctx.state.user;

    if (!user) {
      return ctx.unauthorized('You must be logged in to create a property');
    }

    try {
      // Check membership limits
      const membershipService = strapi.service('api::membership.membership');
      const canCreate = await membershipService.canCreateProperty(user.id);

      if (!canCreate) {
        return ctx.forbidden('You have reached your property limit. Please upgrade your membership.');
      }

      // Remove invalid fields and set owner properly
      const { propertyUrl, owner, ...validData } = ctx.request.body.data;

      // Create property using entityService directly with owner
      const propertyData = {
        ...validData,
        owner: user.id
      };

      const property = await strapi.entityService.create('api::property.property', {
        data: propertyData,
        populate: {
          owner: {
            fields: ['id', 'username', 'email']
          },
          images: true,
          floorPlan: true
        }
      });

      // Handle file uploads if present
      if (ctx.request.files && Object.keys(ctx.request.files).length > 0) {
        console.log('Processing file uploads for property:', property.id);

        // Handle both 'images' and 'files.images' formats
        const images = ctx.request.files.images || ctx.request.files['files.images'];
        const floorPlan = ctx.request.files.floorPlan || ctx.request.files['files.floorPlan'];

        if (images) {
          console.log('Uploading images...');
          console.log('Property ID for upload:', property.id);
          console.log('Property documentId for upload:', property.documentId);
          try {
            const uploadedImages = await strapi.plugins.upload.services.upload.upload({
              data: {
                refId: property.id,
                ref: 'api::property.property',
                field: 'images'
              },
              files: Array.isArray(images) ? images : [images]
            });
            console.log('Images uploaded successfully:', uploadedImages.length, 'files');
            console.log('Uploaded image details:', uploadedImages.map(img => ({ id: img.id, url: img.url })));
          } catch (error) {
            console.error('Error uploading images:', error);
          }
        }

        if (floorPlan) {
          console.log('Uploading floor plan...');
          try {
            const uploadedFloorPlan = await strapi.plugins.upload.services.upload.upload({
              data: {
                refId: property.id,
                ref: 'api::property.property',
                field: 'floorPlan'
              },
              files: Array.isArray(floorPlan) ? floorPlan : [floorPlan]
            });
            console.log('Floor plan uploaded successfully');
          } catch (error) {
            console.error('Error uploading floor plan:', error);
          }
        }

        // Fetch the property again with populated images
        console.log('Fetching property with ID:', property.id);
        const updatedProperty = await strapi.entityService.findOne(
          'api::property.property',
          property.id,
          {
            populate: {
              owner: {
                fields: ['id', 'username', 'email']
              },
              images: true,
              floorPlan: true
            }
          }
        );
        console.log('Fetched property ID:', updatedProperty?.id, 'Expected ID:', property.id);

        console.log('Final property with files:', {
          id: updatedProperty.id,
          propertyId: property.id,
          images: (updatedProperty as any).images?.length || 0,
          floorPlan: (updatedProperty as any).floorPlan ? 'present' : 'none'
        });

        console.log('Returning property data:', {
          id: updatedProperty.id,
          title: updatedProperty.title,
          hasImages: !!(updatedProperty as any).images
        });

        return { data: updatedProperty };
      }

      return { data: property };
    } catch (error) {
      console.error('Error creating property:', error);
      console.error('Error stack:', error.stack);
      console.error('Error message:', error.message);
      console.error('User ID:', user.id);
      console.error('Request body:', JSON.stringify(ctx.request.body, null, 2));
      return ctx.internalServerError(`Failed to create property: ${error.message}`);
    }
  },

  // Custom update method with ownership check
  async update(ctx) {
    const user = ctx.state.user;
    const { id } = ctx.params;



    if (!user) {
      return ctx.unauthorized('You must be logged in to update a property');
    }

    try {
      // Find the property first to check ownership - handle both numeric ID and documentId
      let property;
      let actualId = id; // The ID we'll use for the update

      // Check if ID looks like a documentId (long string) or numeric ID
      const isNumericId = /^\d+$/.test(id);


      if (!isNumericId) {
        // Try documentId first
        try {
          property = await strapi.entityService.findOne(
            'api::property.property',
            id,
            {
              populate: ['owner']
            }
          ) as any;
        } catch (docIdError) {
          property = null;
        }

        // If documentId lookup failed, try manual search
        if (!property) {
          const allProperties = await strapi.entityService.findMany('api::property.property', {
            populate: ['owner']
          }) as any[];

          property = allProperties.find(p => p.documentId === id);
          if (property) {
            actualId = property.id; // Use numeric ID for update
          }
        }
      } else {
        // Try with numeric ID
        try {
          property = await strapi.entityService.findOne(
            'api::property.property',
            parseInt(id),
            {
              populate: ['owner']
            }
          ) as any;
          if (property) {
            actualId = property.id; // Use numeric ID for update
          }
        } catch (numericError) {
          property = null;
        }
      }

      if (!property) {
        return ctx.notFound('Property not found');
      }

      // Check if user owns the property or is admin
      const ownerId = property.owner?.id || property.owner;
      if (ownerId !== user.id && user.role?.name !== 'Admin') {
        return ctx.forbidden('You can only update your own properties');
      }

      // Ensure data is properly structured for the default controller
      if (ctx.request.body.data && typeof ctx.request.body.data === 'string') {
        // If data is a JSON string (from FormData), parse it
        try {
          ctx.request.body.data = JSON.parse(ctx.request.body.data);
        } catch (error) {
          console.error('Failed to parse data JSON:', error);
        }
      } else if (!ctx.request.body.data && ctx.request.body && typeof ctx.request.body === 'object') {
        // If data is passed directly in body, wrap it in data object
        const bodyData = { ...ctx.request.body };
        delete bodyData.files; // Remove files if present
        ctx.request.body = { data: bodyData };
      }

      // Don't allow changing the owner in the data
      if (ctx.request.body.data) {
        delete ctx.request.body.data.owner;
      }

      console.log('Updating property with data:', ctx.request.body.data);

      // Remove invalid fields
      const { propertyUrl, owner, ...validData } = ctx.request.body.data || {};

      // Use entityService to update the property directly
      try {

        const updatedProperty = await strapi.entityService.update(
          'api::property.property',
          actualId,
          {
            data: validData,
            populate: {
              owner: {
                fields: ['id', 'username', 'email']
              },
              agent: {
                fields: ['id', 'username', 'email']
              },
              images: true,
              floorPlan: true,
              project: {
                fields: ['id', 'name', 'slug']
              }
            }
          }
        );



        // Handle file uploads if present
        if (ctx.request.files && Object.keys(ctx.request.files).length > 0) {
          console.log('Processing file uploads for update...');

          const images = ctx.request.files.images || ctx.request.files['files.images'];
          const floorPlan = ctx.request.files.floorPlan || ctx.request.files['files.floorPlan'];

          if (images) {
            console.log('Uploading new images...');
            try {
              await strapi.plugins.upload.services.upload.upload({
                data: {
                  refId: updatedProperty.id,
                  ref: 'api::property.property',
                  field: 'images'
                },
                files: Array.isArray(images) ? images : [images]
              });
              console.log('Images uploaded successfully for update');
            } catch (error) {
              console.error('Error uploading images during update:', error);
            }
          }

          if (floorPlan) {
            console.log('Uploading new floor plan...');
            try {
              await strapi.plugins.upload.services.upload.upload({
                data: {
                  refId: updatedProperty.id,
                  ref: 'api::property.property',
                  field: 'floorPlan'
                },
                files: Array.isArray(floorPlan) ? floorPlan : [floorPlan]
              });
              console.log('Floor plan uploaded successfully for update');
            } catch (error) {
              console.error('Error uploading floor plan during update:', error);
            }
          }

          // Fetch the property again with updated files
          const finalProperty = await strapi.entityService.findOne(
            'api::property.property',
            updatedProperty.id,
            {
              populate: {
                owner: {
                  fields: ['id', 'username', 'email']
                },
                agent: {
                  fields: ['id', 'username', 'email']
                },
                images: true,
                floorPlan: true,
                project: {
                  fields: ['id', 'name', 'slug']
                }
              }
            }
          );

          console.log('Final property after file upload:', {
            id: finalProperty.id,
            images: (finalProperty as any).images?.length || 0
          });

          return { data: finalProperty };
        }


        return { data: updatedProperty };
      } catch (error) {
        console.error('Error updating property:', error);
        throw error;
      }
    } catch (error) {
      console.error('Error updating property:', error);
      return ctx.internalServerError(`Failed to update property: ${error.message}`);
    }
  },

  // Update image order
  async updateImageOrder(ctx) {
    const { id } = ctx.params;
    const { imageOrder } = ctx.request.body;
    const user = ctx.state.user;

    if (!user) {
      return ctx.unauthorized('You must be logged in');
    }

    try {
      // Find property and check ownership
      let property;
      let actualId = id;

      // Handle both numeric ID and documentId
      const isNumericId = /^\d+$/.test(id);
      if (!isNumericId) {
        try {
          property = await strapi.entityService.findOne('api::property.property', id, {
            populate: ['owner', 'images']
          }) as any;
        } catch (docIdError) {
          const allProperties = await strapi.entityService.findMany('api::property.property', {
            populate: ['owner', 'images']
          }) as any[];
          property = allProperties.find(p => p.documentId === id);
          if (property) {
            actualId = property.id;
          }
        }
      } else {
        property = await strapi.entityService.findOne('api::property.property', parseInt(id), {
          populate: ['owner', 'images']
        }) as any;
        if (property) {
          actualId = property.id;
        }
      }

      if (!property) {
        return ctx.notFound('Property not found');
      }

      // Check ownership
      const ownerId = property.owner?.id || property.owner;
      if (ownerId !== user.id && user.role?.type !== 'admin') {
        return ctx.forbidden('You can only update your own properties');
      }

      // Update image order by updating the images relation
      if (imageOrder && Array.isArray(imageOrder)) {
        await strapi.entityService.update('api::property.property', actualId, {
          data: {
            images: imageOrder
          }
        });
      }

      return { success: true };
    } catch (error) {
      console.error('Error updating image order:', error);
      return ctx.internalServerError('Failed to update image order');
    }
  },

  // Custom delete method to check ownership
  async delete(ctx) {
    const { id } = ctx.params;
    const user = ctx.state.user;

    if (!user) {
      return ctx.unauthorized('You must be logged in to delete a property');
    }

    const property = await strapi.entityService.findOne('api::property.property', id, {
      populate: ['owner']
    }) as any;

    if (!property) {
      return ctx.notFound('Property not found');
    }

    // Check if user is owner or admin
    // @ts-ignore
    const ownerId = property.owner?.id || property.owner;
    if (ownerId !== user.id && user.role?.name !== 'Admin') {
      return ctx.forbidden('You can only delete your own properties');
    }

    const response = await super.delete(ctx);
    return response;
  },

  // Get features options from enum definition
  async getFeaturesOptions(ctx) {
    try {
      const schema = strapi.getModel('api::property.property');
      const featuresField = schema.attributes.featuresEnum;

      if (featuresField && featuresField.enum) {
        const features = featuresField.enum.map(value => ({
          value,
          label: formatEnumLabel(value)
        }));

        return ctx.send({ data: features });
      }

      return ctx.send({ data: [] });
    } catch (error) {
      console.error('Error fetching features options:', error);
      return ctx.internalServerError('Failed to fetch features options');
    }
  },



  // Get properties by user using reusable helper
  async getMyProperties(ctx) {
    const user = ctx.state.user;

    if (!user) {
      return ctx.unauthorized('You must be logged in');
    }

    try {
      // Use the reusable fetchProperties helper with dashboard population
      const result = await fetchProperties({
        query: ctx.query,
        filters: { owner: user.id }, // Always filter by current user
        populationType: 'dashboard',
        pagination: { page: 1, pageSize: 12 },
        defaultSort: { createdAt: 'desc' }
      });

      // Debug logging
      console.log(`[DEBUG] My Properties - User: ${user.id}, Page: ${result.meta?.pagination?.page}, PageSize: ${result.meta?.pagination?.pageSize}, Total: ${result.meta?.pagination?.total}, Returned: ${result.data.length} properties`);

      return result;
    } catch (error) {
      console.error('Error fetching user properties:', error);
      return ctx.internalServerError('Failed to fetch properties');
    }
  },

  // Get featured properties using reusable helper
  async getFeatured(ctx) {
    try {
      // Use the reusable fetchProperties helper with featured population
      const result = await fetchProperties({
        query: ctx.query,
        filters: {
          featured: true,
          publishedAt: { $notNull: true }
        },
        populationType: 'featured',
        pagination: { page: 1, pageSize: 6 },
        defaultSort: { createdAt: 'desc' },
        includeCount: false // Featured properties don't need pagination metadata
      });

      return { data: result.data };
    } catch (error) {
      console.error('Error fetching featured properties:', error);
      return ctx.internalServerError('Failed to fetch featured properties');
    }
  },









  // Chartbrew data endpoint for analytics integration
  async getChartbrewData(ctx) {
    try {
      const user = ctx.state.user;
      if (!user) {
        return ctx.unauthorized('Authentication required');
      }

      const properties = await strapi.db.query('api::property.property').findMany({
        where: { owner: user.id },
        select: ['id', 'documentId', 'title', 'views', 'publishedAt', 'createdAt'],
        orderBy: { createdAt: 'desc' }
      });

      const formattedData = properties.map(property => ({
        property_id: property.id,
        title: property.title,
        views: property.views || 0,
        status: property.publishedAt ? 'published' : 'draft',
        created_date: property.createdAt,
        user_id: user.id
      }));

      return { data: formattedData };
    } catch (error) {
      return ctx.internalServerError('Failed to fetch data for Chartbrew');
    }
  },

  // Publish property
  async publish(ctx) {
    const { id } = ctx.params;
    const user = ctx.state.user;

    if (!user) {
      return ctx.unauthorized('You must be logged in');
    }

    try {
      // Try to find by documentId first (Strapi v5), then by numeric ID
      let property;
      let actualId = id; // The ID we'll use for the update

      // Check if ID looks like a documentId (long string) or numeric ID
      const isNumericId = /^\d+$/.test(id);

      if (!isNumericId) {
        // Try with documentId using findOne directly
        try {
          property = await strapi.entityService.findOne(
            'api::property.property',
            id,
            {
              populate: ['owner']
            }
          ) as any;
          if (property) {
            actualId = property.documentId; // Use documentId for update
          }
        } catch (error) {
          property = null;
        }

        // If documentId lookup didn't work, try manual matching
        if (!property) {
          try {
            const allProperties = await strapi.entityService.findMany(
              'api::property.property',
              {
                populate: ['owner']
              }
            ) as any[];

            property = allProperties.find(p => p.documentId === id) || null;
            if (property) {
              actualId = property.documentId; // Use documentId for update
            }
          } catch (manualError) {
            property = null;
          }
        }
      } else {
        // Try with numeric ID
        try {
          property = await strapi.entityService.findOne(
            'api::property.property',
            parseInt(id),
            {
              populate: ['owner']
            }
          ) as any;
          if (property) {
            actualId = property.documentId; // Use documentId for update
          }
        } catch (numericError) {
          property = null;
        }
      }

      if (!property) {
        return ctx.notFound('Property not found');
      }

      // Check if user is owner or admin
      const ownerId = property.owner?.id || property.owner;
      if (ownerId !== user.id && user.role?.name !== 'Admin') {
        return ctx.forbidden('You can only publish your own properties');
      }

      const updatedProperty = await strapi.entityService.update('api::property.property', actualId, {
        data: {
          publishedAt: new Date()
        }
      });

      return { data: updatedProperty };
    } catch (error) {
      console.error('Error publishing property:', error);
      return ctx.internalServerError('Failed to publish property');
    }
  },

  // Unpublish property
  async unpublish(ctx) {
    const { id } = ctx.params;
    const user = ctx.state.user;

    if (!user) {
      return ctx.unauthorized('You must be logged in');
    }

    try {
      // Try to find by documentId first (Strapi v5), then by numeric ID
      let property;
      let actualId = id; // The ID we'll use for the update

      // Check if ID looks like a documentId (long string) or numeric ID
      const isNumericId = /^\d+$/.test(id);

      if (!isNumericId) {
        // Try with documentId using findOne directly
        try {
          property = await strapi.entityService.findOne(
            'api::property.property',
            id,
            {
              populate: ['owner']
            }
          ) as any;
          if (property) {
            actualId = property.documentId; // Use documentId for update
          }
        } catch (error) {
          property = null;
        }

        // If documentId lookup didn't work, try manual matching
        if (!property) {
          try {
            const allProperties = await strapi.entityService.findMany(
              'api::property.property',
              {
                populate: ['owner']
              }
            ) as any[];

            property = allProperties.find(p => p.documentId === id) || null;
            if (property) {
              actualId = property.documentId; // Use documentId for update
            }
          } catch (manualError) {
            property = null;
          }
        }
      } else {
        // Try with numeric ID
        try {
          property = await strapi.entityService.findOne(
            'api::property.property',
            parseInt(id),
            {
              populate: ['owner']
            }
          ) as any;
          if (property) {
            actualId = property.documentId; // Use documentId for update
          }
        } catch (numericError) {
          property = null;
        }
      }

      if (!property) {
        return ctx.notFound('Property not found');
      }

      // Check if user is owner or admin
      const ownerId = property.owner?.id || property.owner;
      if (ownerId !== user.id && user.role?.name !== 'Admin') {
        return ctx.forbidden('You can only unpublish your own properties');
      }

      const updatedProperty = await strapi.entityService.update('api::property.property', actualId, {
        data: {
          publishedAt: null
        }
      });

      return { data: updatedProperty };
    } catch (error) {
      console.error('Error unpublishing property:', error);
      return ctx.internalServerError('Failed to unpublish property');
    }
  },

  // Generate nearby places for a property
  async generateNearbyPlaces(ctx) {
    const { id } = ctx.params;
    const user = ctx.state.user;

    if (!user) {
      return ctx.unauthorized('You must be logged in');
    }

    try {
      const property = await strapi.entityService.findOne('api::property.property', id, {
        populate: ['owner']
      }) as any;

      if (!property) {
        return ctx.notFound('Property not found');
      }

      // Note: Nearby places generation is allowed for any authenticated user
      // since it only uses public Google Places API data and doesn't expose private property information

      // Check if property has coordinates
      if (!property.coordinates || !property.coordinates.lat || !property.coordinates.lng) {
        return ctx.badRequest('Property must have coordinates to generate nearby places');
      }

      // Get enabled place categories
      const categories = await strapi.entityService.findMany('api::nearby-place-category.nearby-place-category', {
        filters: { enabled: true },
        sort: { priority: 'desc' }
      });

      if (categories.length === 0) {
        return ctx.badRequest('No place categories are enabled');
      }

      // Generate nearby places for each category
      const propertyService = strapi.service('api::property.property');
      const nearbyPlaces = {};

      for (const category of categories) {
        try {
          const places = await propertyService.findNearbyPlaces({
            lat: property.coordinates.lat,
            lng: property.coordinates.lng,
            types: category.googlePlaceTypes,
            radius: category.searchRadius,
            maxResults: category.maxResults
          });

          nearbyPlaces[category.name] = {
            category: {
              id: category.id,
              name: category.name,
              displayName: category.displayName,
              icon: category.icon,
              color: category.color
            },
            places: places
          };
        } catch (error) {
          console.error(`Error fetching places for category ${category.name}:`, error.message);
          nearbyPlaces[category.name] = {
            category: {
              id: category.id,
              name: category.name,
              displayName: category.displayName,
              icon: category.icon,
              color: category.color
            },
            places: [],
            error: error.message
          };
        }
      }

      // Update property with nearby places
      const updatedProperty = await strapi.entityService.update('api::property.property', id, {
        data: {
          nearbyPlaces: nearbyPlaces
        }
      });

      return { data: { nearbyPlaces: nearbyPlaces } };
    } catch (error) {
      console.error('Error generating nearby places:', error);
      return ctx.internalServerError('Failed to generate nearby places');
    }
  },

  // Get nearby places for a property
  async getNearbyPlaces(ctx) {
    const { id } = ctx.params;

    try {
      const property = await strapi.entityService.findOne('api::property.property', id, {
        fields: ['nearbyPlaces']
      }) as any;

      if (!property) {
        return ctx.notFound('Property not found');
      }

      return { data: property.nearbyPlaces || {} };
    } catch (error) {
      console.error('Error fetching nearby places:', error);
      return ctx.internalServerError('Failed to fetch nearby places');
    }
  },

  // Find nearby places for given coordinates (used by frontend map preview)
  async findNearbyPlaces(ctx) {


    try {
      const { lat, lng, types, radius = 1500, maxResults = 10 } = ctx.request.body;

      // Validate required parameters
      if (!lat || !lng) {
        return ctx.badRequest('Latitude and longitude are required');
      }

      if (!types || !Array.isArray(types) || types.length === 0) {
        return ctx.badRequest('Place types array is required');
      }

      // Use the property service with Google Places functionality
      const propertyService = strapi.service('api::property.property');

      const places = await propertyService.findNearbyPlaces({
        lat: parseFloat(lat),
        lng: parseFloat(lng),
        types,
        radius: parseInt(radius),
        maxResults: parseInt(maxResults)
      });

      return { data: places };
    } catch (error) {
      console.error('Error finding nearby places:', error.message);
      return ctx.internalServerError(`Failed to find nearby places: ${error.message}`);
    }
  }
}));
